#!/usr/bin/env python3
"""
Test script for AsyncInprocClient implementation.
This script demonstrates the new asyncio mode without multiprocessing.
"""

import asyncio
import sys
import os

# Add the vllm directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from vllm.config import VllmConfig, ModelConfig, ParallelConfig, CacheConfig, SchedulerConfig
from vllm.v1.engine.core_client import EngineCoreClient
from vllm.v1.engine import EngineCoreRequest
from vllm.v1.executor.gpu_executor import GPUExecutor


def create_test_config():
    """Create a minimal test configuration."""
    model_config = ModelConfig(
        model="facebook/opt-125m",  # Small model for testing
        tokenizer="facebook/opt-125m",
        tokenizer_mode="auto",
        trust_remote_code=False,
        dtype="auto",
        seed=0,
        runner_type="generate",
    )
    
    parallel_config = ParallelConfig(
        pipeline_parallel_size=1,
        tensor_parallel_size=1,
        worker_use_ray=False,
        max_parallel_loading_workers=None,
        disable_custom_all_reduce=False,
        tokenizer_pool_size=0,
        tokenizer_pool_type="ray",
        tokenizer_pool_extra_config=None,
        placement_group=None,
        data_parallel_size=1,
    )
    
    cache_config = CacheConfig(
        block_size=16,
        gpu_memory_utilization=0.9,
        swap_space=4,
        cache_dtype="auto",
        num_gpu_blocks=None,
        num_cpu_blocks=None,
        sliding_window=None,
        enable_prefix_caching=False,
        cpu_offload_gb=0,
    )
    
    scheduler_config = SchedulerConfig(
        max_num_batched_tokens=2048,
        max_num_seqs=256,
        max_model_len=2048,
        use_v2_block_manager=False,
        num_lookahead_slots=0,
        delay_factor=0.0,
        enable_chunked_prefill=False,
        embedding_mode=False,
        preemption_mode="swap",
        num_scheduler_steps=1,
        multi_step_stream_outputs=True,
        send_delta_data=False,
    )
    
    return VllmConfig(
        model_config=model_config,
        parallel_config=parallel_config,
        cache_config=cache_config,
        scheduler_config=scheduler_config,
    )


async def test_async_inproc_client():
    """Test the AsyncInprocClient implementation."""
    print("Testing AsyncInprocClient...")
    
    try:
        # Create test configuration
        vllm_config = create_test_config()
        
        # Test the new asyncio mode without multiprocessing
        print("Creating AsyncInprocClient...")
        client = EngineCoreClient.make_client(
            multiprocess_mode=False,
            asyncio_mode=True,
            vllm_config=vllm_config,
            executor_class=GPUExecutor,
            log_stats=False,
        )
        
        print(f"Client type: {type(client).__name__}")
        
        # Test basic functionality
        print("Testing basic async operations...")
        
        # Create a simple test request
        test_request = EngineCoreRequest(
            request_id="test_request_1",
            inputs="Hello, how are you?",
            params=None,  # Will use default sampling params
            arrival_time=0.0,
        )
        
        # Add request asynchronously
        print("Adding test request...")
        await client.add_request_async(test_request)
        
        # Get output asynchronously
        print("Getting output...")
        output = await client.get_output_async()
        print(f"Received output: {output}")
        
        # Test utility methods
        print("Testing utility methods...")
        is_sleeping = await client.is_sleeping_async()
        print(f"Is sleeping: {is_sleeping}")
        
        # Cleanup
        print("Shutting down client...")
        client.shutdown()
        
        print("Test completed successfully!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def main():
    """Main test function."""
    print("Starting AsyncInprocClient test...")
    
    # Test that the new mode is supported
    try:
        vllm_config = create_test_config()
        
        # This should now work (previously raised NotImplementedError)
        client = EngineCoreClient.make_client(
            multiprocess_mode=False,
            asyncio_mode=True,
            vllm_config=vllm_config,
            executor_class=GPUExecutor,
            log_stats=False,
        )
        
        print(f"✓ Successfully created client: {type(client).__name__}")
        client.shutdown()
        
    except NotImplementedError as e:
        print(f"✗ Still not implemented: {e}")
        return False
    except Exception as e:
        print(f"✗ Other error: {e}")
        return False
    
    # Run full test
    success = await test_async_inproc_client()
    
    if success:
        print("\n✓ All tests passed!")
    else:
        print("\n✗ Some tests failed!")
    
    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
