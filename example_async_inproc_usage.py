#!/usr/bin/env python3
"""
Example usage of the new AsyncInprocClient for debugging purposes.

This demonstrates how to use vLLM v1 in asyncio mode without multiprocessing,
which is useful for debugging and development.
"""

import asyncio
from vllm.v1.engine.core_client import EngineCoreClient
from vllm.v1.engine import Engine<PERSON><PERSON>Request
from vllm.config import VllmConfig


async def debug_example():
    """
    Example showing how to use AsyncInprocClient for debugging.
    
    This mode is useful when you want:
    1. Asyncio behavior for non-blocking operations
    2. Single-process execution for easier debugging
    3. No ZMQ/multiprocessing complexity
    """
    
    # Create your vLLM configuration
    vllm_config = VllmConfig(...)  # Your config here
    
    # Create client in asyncio mode WITHOUT multiprocessing
    # This was previously not supported and would raise NotImplementedError
    client = EngineCoreClient.make_client(
        multiprocess_mode=False,  # Single process
        asyncio_mode=True,        # Asyncio support
        vllm_config=vllm_config,
        executor_class=YourExecutorClass,
        log_stats=False,
    )
    
    print(f"Created client: {type(client).__name__}")  # Should be AsyncInprocClient
    
    try:
        # Use async methods for non-blocking operations
        request = EngineCoreRequest(
            request_id="debug_request",
            inputs="What is the meaning of life?",
            params=your_sampling_params,
            arrival_time=0.0,
        )
        
        # Add request asynchronously
        await client.add_request_async(request)
        
        # Get outputs asynchronously
        while True:
            output = await client.get_output_async()
            
            if output.outputs:
                for req_output in output.outputs:
                    print(f"Request {req_output.request_id}: {req_output}")
                    
                    if req_output.finished:
                        print("Request completed!")
                        break
            
            # You can do other async work here
            await asyncio.sleep(0.01)
        
        # Test utility methods
        is_sleeping = await client.is_sleeping_async()
        print(f"Engine sleeping: {is_sleeping}")
        
    finally:
        # Clean shutdown
        client.shutdown()


async def comparison_example():
    """
    Comparison of different client modes.
    """
    
    vllm_config = VllmConfig(...)  # Your config
    
    # 1. Traditional sync mode (single process)
    sync_client = EngineCoreClient.make_client(
        multiprocess_mode=False,
        asyncio_mode=False,
        vllm_config=vllm_config,
        executor_class=YourExecutorClass,
        log_stats=False,
    )
    print(f"Sync client: {type(sync_client).__name__}")  # InprocClient
    
    # 2. NEW: Async mode (single process) - for debugging
    async_inproc_client = EngineCoreClient.make_client(
        multiprocess_mode=False,
        asyncio_mode=True,  # This is now supported!
        vllm_config=vllm_config,
        executor_class=YourExecutorClass,
        log_stats=False,
    )
    print(f"Async inproc client: {type(async_inproc_client).__name__}")  # AsyncInprocClient
    
    # 3. Async mode (multiprocess) - production
    async_mp_client = EngineCoreClient.make_client(
        multiprocess_mode=True,
        asyncio_mode=True,
        vllm_config=vllm_config,
        executor_class=YourExecutorClass,
        log_stats=False,
    )
    print(f"Async MP client: {type(async_mp_client).__name__}")  # AsyncMPClient
    
    # Clean up
    sync_client.shutdown()
    async_inproc_client.shutdown()
    async_mp_client.shutdown()


def main():
    """
    Main function showing the usage patterns.
    """
    print("AsyncInprocClient Usage Examples")
    print("=" * 40)
    
    print("\n1. Debug Example:")
    asyncio.run(debug_example())
    
    print("\n2. Comparison Example:")
    asyncio.run(comparison_example())
    
    print("\nKey Benefits of AsyncInprocClient:")
    print("- Asyncio support for non-blocking operations")
    print("- Single process for easier debugging")
    print("- No ZMQ/multiprocessing complexity")
    print("- Compatible with existing async codebases")
    print("- Useful for development and testing")


if __name__ == "__main__":
    main()
